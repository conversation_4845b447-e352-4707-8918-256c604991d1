import React from "react";

const StatusCard = ({ status, value, icon, color = "indigo" }) => {
  const bgColor = {
    indigo: "bg-indigo-100 text-indigo-700",
    green: "bg-green-100 text-green-700",
    blue: "bg-blue-100 text-blue-700",
    purple: "bg-purple-100 text-purple-700",
    yellow: "bg-yellow-100 text-yellow-700",
    red: "bg-red-100 text-red-700",
  }[color] || "bg-indigo-100 text-indigo-700";

  return (
    <div className="w-60 h-20 px-4 py-3 bg-white rounded-2xl border border-gray-200 flex items-center gap-4 shadow-sm">
      <div className={`w-12 h-12 flex items-center justify-center rounded-full ${bgColor}`}>{icon}</div>
      <div className="flex flex-col justify-center">
        <div className="text-lg font-bold text-gray-900 leading-6">{value}</div>
        <div className="text-xs font-medium text-gray-500 leading-none">{status}</div>
      </div>
    </div>
  );
};

export default StatusCard;
