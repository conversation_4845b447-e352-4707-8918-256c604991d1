{"ast": null, "code": "var _jsxFileName = \"D:\\\\WORK\\\\Test\\\\src\\\\App.js\";\nimport React from \"react\";\nimport Main from \"./MainComponent\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 p-4\",\n    children: /*#__PURE__*/_jsxDEV(Main, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Main", "jsxDEV", "_jsxDEV", "App", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/WORK/Test/src/App.js"], "sourcesContent": ["import React from \"react\";\r\nimport Main from \"./MainComponent\";\r\n\r\nfunction App() {\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50 p-4\">\r\n      <Main />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnC,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAKE,SAAS,EAAC,6BAA6B;IAAAC,QAAA,eAC1CH,OAAA,CAACF,IAAI;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV;AAACC,EAAA,GANQP,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}