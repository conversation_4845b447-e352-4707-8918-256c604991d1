import React from "react";
import Header from "./Header";
import StatisticCard from "./StatisticCard";
import ProfileCard from "./ProfileCard";
import StatusCard from "./StatusCard";
import AssessmentCard from "./AssessmentCard";

const Dashboard = () => {
  return (
    <div className="min-h-screen bg-gray-50 p-6 flex flex-col gap-6">
      {/* Header */}
      <Header />
      <div className="flex gap-6">
        {/* Main Section */}
        <div className="flex-1 flex flex-col gap-6">
          {/* Statistic Cards */}
          <div className="flex gap-4">
            <StatisticCard value={3} label="Total Analysis" color="indigo" />
            <StatisticCard value={4} label="Completed" color="green" />
            <StatisticCard value={6} label="Processing" color="blue" />
            <StatisticCard value={2} label="Token Balance" color="purple" />
          </div>
          {/* Assessment Table (as Cards) */}
          <div className="bg-white rounded-xl shadow-md p-6">
            <div className="flex justify-between items-center mb-4">
              <div>
                <div className="text-black text-xl font-semibold">Assessment History</div>
                <div className="text-gray-500 text-xs">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut et massa mi. Aliquam in hendrerit urna.</div>
              </div>
              <button className="px-4 py-2 bg-indigo-500 text-white rounded-lg text-xs font-bold">New Assessment</button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
              <AssessmentCard nomor={1} nama="Matematika" tipeUjian="PG" tanggalUjian="12 Juni 2024" />
              <AssessmentCard nomor={2} nama="Bahasa Inggris" tipeUjian="PG" tanggalUjian="12 Juni 2024" />
              <AssessmentCard nomor={3} nama="Bahasa Indonesia" tipeUjian="PG" tanggalUjian="22 Juni 2024" />
              <AssessmentCard nomor={4} nama="Biologi" tipeUjian="Essay" tanggalUjian="22 Juni 2024" />
            </div>
          </div>
        </div>
        {/* Sidebar */}
        <div className="w-full max-w-xs flex flex-col gap-6">
          <ProfileCard name="Lorem ipsum dolor sit amet" description="Lorem ipsum dolor sit amet, consectetur adipiscing elit." avatarUrl="https://placehold.co/96x96" />
          <div className="flex flex-col gap-4">
            <StatusCard status="Active" value={"86%"} icon={<span>✔️</span>} color="green" />
            <StatusCard status="Pending" value={"32%"} icon={<span>⏳</span>} color="yellow" />
            <StatusCard status="Error" value={"12%"} icon={<span>❌</span>} color="red" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
