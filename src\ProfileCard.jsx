import React from "react";

const ProfileCard = ({ name, description, avatarUrl }) => {
  return (
    <div className="bg-white rounded-xl border border-gray-200 p-6 flex flex-col items-center gap-4 w-full max-w-xs mx-auto">
      <div className="w-32 h-32 relative mb-2">
        <div className="w-32 h-32 bg-indigo-200 rounded-full" />
        <div className="w-36 h-24 absolute bg-indigo-500 rounded-full transform rotate-[-135deg] top-0 left-0" />
        <img className="w-24 h-24 absolute top-4 left-4 rounded-full object-cover border-4 border-white" src={avatarUrl || "https://placehold.co/96x96"} alt="Profile" />
      </div>
      <div className="text-center">
        <div className="text-black text-xl font-semibold leading-8">{name || "Nama Pengguna"}</div>
        <div className="text-gray-500 text-xs font-normal leading-none">{description || "Deskripsi singkat pengguna."}</div>
      </div>
      <div className="flex justify-between w-full px-6 mt-2">
        <div className="w-6 h-6 bg-purple-100 rounded-full" />
        <div className="w-6 h-6 bg-purple-100 rounded-full" />
        <div className="w-6 h-6 bg-purple-100 rounded-full" />
        <div className="w-6 h-6 bg-purple-100 rounded-full" />
        <div className="w-6 h-6 bg-purple-100 rounded-full" />
      </div>
    </div>
  );
};

export default ProfileCard;
