import React from "react";

const StatisticCard = ({ value, label, color = "indigo" }) => {
  const bgColor = {
    indigo: "bg-indigo-100",
    green: "bg-green-100",
    blue: "bg-blue-100",
    purple: "bg-purple-100",
    yellow: "bg-yellow-100",
    red: "bg-red-100",
  }[color] || "bg-indigo-100";

  return (
    <div className="w-52 h-16 px-3.5 py-3 bg-white rounded-2xl border border-gray-200 flex justify-start items-center gap-2.5">
      <div className="flex-1 flex flex-col justify-center items-center">
        <div className="text-stone-900 text-3xl font-semibold leading-9">{value}</div>
        <div className="text-gray-800 text-[10px] font-light leading-3">{label}</div>
      </div>
      <div className={`w-12 h-12 ${bgColor} rounded-full`} />
    </div>
  );
};

export default StatisticCard;
