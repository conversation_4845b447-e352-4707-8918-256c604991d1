import React from "react";

const AssessmentCard = ({ nomor, nama, tipe<PERSON><PERSON>an, tangg<PERSON><PERSON><PERSON><PERSON> }) => {
  return (
    <div className="bg-white rounded-xl shadow-md p-4 flex flex-col gap-2 w-full max-w-md mx-auto">
      <div className="flex justify-between items-center mb-2">
        <span className="text-xs text-gray-500 font-medium">Nomor</span>
        <span className="text-xs text-gray-500 font-medium">Tanggal Ujian</span>
      </div>
      <div className="flex justify-between items-center mb-2">
        <span className="text-lg font-bold text-indigo-700">{nomor}</span>
        <span className="text-sm font-semibold text-gray-800">{tanggal<PERSON>jian}</span>
      </div>
      <div className="mb-2">
        <span className="block text-sm font-semibold text-black">{nama}</span>
        <span className="block text-xs text-gray-500">{tipe<PERSON><PERSON>an}</span>
      </div>
      <div className="flex justify-end gap-2 mt-2">
        <button className="px-3 py-1 bg-indigo-500 text-white rounded-lg text-xs font-bold">Detail</button>
        <button className="px-3 py-1 bg-gray-100 text-gray-700 rounded-lg text-xs font-bold">Edit</button>
      </div>
    </div>
  );
};

export default AssessmentCard;
