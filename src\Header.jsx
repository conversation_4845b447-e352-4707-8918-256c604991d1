import React from "react";

const Header = ({ title, subtitle, onLogout }) => {
  return (
    <header className="w-full flex justify-between items-center py-6 px-8 bg-white rounded-xl shadow-sm mb-6">
      <div className="flex flex-col">
        <h1 className="text-black text-3xl font-semibold leading-[48px]">{title || "Lorem ipsum dolor sit amet"}</h1>
        <p className="text-black text-xs font-normal leading-none mt-1">{subtitle || "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut et massa mi. Aliquam in hendrerit urna."}</p>
      </div>
      <button
        className="px-5 py-2 bg-indigo-500 text-white rounded-lg text-sm font-bold shadow hover:bg-indigo-600 transition"
        onClick={onLogout}
      >
        Log out
      </button>
    </header>
  );
};

export default Header;
